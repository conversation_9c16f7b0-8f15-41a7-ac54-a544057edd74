FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && \
    apt-get install -y curl build-essential && \
    rm -rf /var/lib/apt/lists/*

# Install uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# Setup workspace
WORKDIR /usr/src/app
RUN mkdir -p /var/log /usr/src/app/resources

# Install Python dependencies
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-cache --no-dev

# Copy application
COPY . .

# Fix Flask-Admin fonts
RUN ln -s \
    /usr/src/app/.venv/lib/python3.12/site-packages/flask_admin/static/bootstrap/bootstrap3/swatch/fonts \
    /usr/src/app/.venv/lib/python3.12/site-packages/flask_admin/static/bootstrap/bootstrap3/fonts

EXPOSE 8001
