from typing import List, Optional
from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.adapters.profit_center_template_adapter import ProfitCenterTemplateAdapter
from cataloging_service.domain.entities.templates.profit_center_template import ProfitCenterTemplateEntity
from cataloging_service.models import ProfitCenterTemplate, BrandProfitCenterTemplateMap


@register_instance()
class ProfitCenterTemplateRepository(BaseEntityRepository):
    """Repository for profit center template operations"""

    adaptor = ProfitCenterTemplateAdapter()

    def create(self, entity: ProfitCenterTemplateEntity) -> ProfitCenterTemplateEntity:
        """Create a new profit center template"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_id(self, template_id: int) -> Optional[ProfitCenterTemplateEntity]:
        """Get profit center template by ID"""
        model = self.rget_by_attr(ProfitCenterTemplate, limit_one=True, id=template_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_brand_and_code(self, brand_id: int, code: str) -> Optional[ProfitCenterTemplateEntity]:
        """Get profit center template by brand ID and code (M2M relationship)"""
        query = (
            self.session()
            .query(ProfitCenterTemplate)
            .join(BrandProfitCenterTemplateMap,
                  ProfitCenterTemplate.id == BrandProfitCenterTemplateMap.profit_center_template_id)
            .filter(
                BrandProfitCenterTemplateMap.brand_id == brand_id,
                ProfitCenterTemplate.code == code
            )
            .limit(1)
        )
        model = query.one_or_none()
        return self.adaptor.to_entity(model) if model else None

    def get_by_code(self, code: str) -> Optional[ProfitCenterTemplateEntity]:
        """Get profit center template by code"""
        model = self.rget_by_attr(ProfitCenterTemplate, limit_one=True, code=code)
        return self.adaptor.to_entity(model) if model else None

    def get_by_brand(self, brand_id: int, active_only: bool = True) -> List[ProfitCenterTemplateEntity]:
        """Get all department templates for a given brand (via M2M)"""
        query = self.session().query(ProfitCenterTemplate).join(
            BrandProfitCenterTemplateMap,
            BrandProfitCenterTemplateMap.department_template_id == ProfitCenterTemplate.id
        ).filter(
            BrandProfitCenterTemplateMap.brand_id == brand_id
        )

        if active_only:
            query = query.filter(ProfitCenterTemplate.is_active == True)

        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_department_template(self, brand_id: int, department_template_code: str) -> List[ProfitCenterTemplateEntity]:
        """Get profit center templates for a specific department template"""
        models = self.rget_by_attr(
            ProfitCenterTemplate,
            brand_id=brand_id,
            department_template_code=department_template_code,
            is_active=True
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_auto_create_templates(self, brand_id: int) -> List[ProfitCenterTemplateEntity]:
        """Get profit center templates that should be auto-created on property launch"""
        models = self.session().query(ProfitCenterTemplate).join(
            BrandProfitCenterTemplateMap,
            BrandProfitCenterTemplateMap.profit_center_template_id == ProfitCenterTemplate.id
        ).filter(
            BrandProfitCenterTemplateMap.brand_id == brand_id,
            ProfitCenterTemplate.is_active == True,
            ProfitCenterTemplate.auto_create_on_property_launch == True
        ).all()
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: ProfitCenterTemplateEntity) -> ProfitCenterTemplateEntity:
        """Update profit center template"""
        existing_model = self.rget_by_attr(ProfitCenterTemplate, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Profit center template with ID {entity.id} not found")
        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, template_id: int) -> bool:
        """Delete profit center template"""
        model = self.rget_by_attr(ProfitCenterTemplate, limit_one=True, id=template_id)
        if not model:
            return False
        super().delete(model)
        return True

    def exists_by_brand_and_code(self, brand_id: int, code: str, exclude_id: Optional[int] = None) -> bool:
        """Check if a department template with the given code exists for a brand"""
        query = (
            self.session().query(ProfitCenterTemplate)
            .join(BrandProfitCenterTemplateMap,
                  ProfitCenterTemplate.id == BrandProfitCenterTemplateMap.profit_center_template_id)
            .filter(
                BrandProfitCenterTemplateMap.brand_id == brand_id,
                ProfitCenterTemplate.code == code
            )
        )
        if exclude_id:
            query = query.filter(ProfitCenterTemplate.id != exclude_id)

        return query.first() is not None

