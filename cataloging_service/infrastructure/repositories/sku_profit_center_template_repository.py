from typing import Optional, List
from cataloging_service.models import SkuProfit<PERSON>enterTemplateMap, Sku
from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.adapters.sku_profit_center_template_adapter import SkuProfit<PERSON>enterTemplateAdapter
from cataloging_service.domain.entities.templates.profit_center_template import SkuProfit<PERSON>enterTemplateMappingEntity


@register_instance()
class SkuProfitCenterTemplateRepository(BaseEntityRepository):
    """Repository for sku profit center template operations"""

    adaptor = SkuProfitCenterTemplateAdapter()

    def create(self, entity: SkuProfitCenterTemplateMappingEntity) -> SkuProfitCenterTemplateMappingEntity:
        """Create a new sku profit center template mapping"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_brand_id(self, brand_id: int) -> Optional[List[SkuProfitCenterTemplateMappingEntity]]:
        """Get sku profit center template mappings by brand ID"""
        models = self.rget_by_attr(SkuProfitCenterTemplateMap, brand_id=brand_id)
        return [self.adaptor.to_entity(model) for model in models] if models else None

    def get_skus_by_brand_and_profit_center_template(self, brand_id: int, profit_center_template_id: int) -> List[Sku]:
        """Get SKUs that are mapped to a specific brand and profit center template"""
        query = (
            self.session()
            .query(Sku)
            .join(SkuProfitCenterTemplateMap, Sku.id == SkuProfitCenterTemplateMap.sku_id)
            .filter(
                SkuProfitCenterTemplateMap.brand_id == brand_id,
                SkuProfitCenterTemplateMap.profit_center_template_id == profit_center_template_id,
                Sku.auto_create_seller_sku == True,
                Sku.is_active == True
            )
        )
        return query.all()
