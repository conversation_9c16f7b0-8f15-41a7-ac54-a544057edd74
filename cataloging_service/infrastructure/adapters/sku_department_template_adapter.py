from cataloging_service.domain.entities.templates.profit_center_template import SkuProfitCenterTemplateMappingEntity
from cataloging_service.models import SkuProfitCenterTemplateMap


class SkuProfitCenterTemplateAdapter:
    """Adapter for sku profit center template entity and model conversion"""

    @staticmethod
    def to_entity(model: SkuProfitCenterTemplateMap) -> SkuProfitCenterTemplateMappingEntity:
        """Convert database model to domain entity"""
        return SkuProfitCenterTemplateMappingEntity(
            brand_id=model.brand_id,
            profit_center_template_id=model.profit_center_template_id,
            sku_id=model.sku_id,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: SkuProfitCenterTemplateMappingEntity) -> SkuProfitCenterTemplateMap:
        """Convert domain entity to database model"""
        model = SkuProfitCenterTemplateMap()
        model.sku_id = entity.sku_id
        model.profit_center_template_id = entity.profit_center_template_id
        model.brand_id = entity.brand_id
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model

    @staticmethod
    def update_model_from_entity(model: SkuProfitCenterTemplateMap, entity: SkuProfitCenterTemplateMappingEntity) -> SkuProfitCenterTemplateMappingEntity:
        model.sku_id = entity.sku_id
        model.profit_center_template_id = entity.profit_center_template_id
        model.brand_id = entity.brand_id
        return model
