from flask_marshmallow import Schema
from marshmallow import fields, validates_schema

from cataloging_service.domain import service_provider

property_service = service_provider.property_service


class UserRoleDetailSchema(Schema):
    first_name = fields.String(required=True)
    last_name = fields.String()
    email = fields.Email(required=True)
    role = fields.String(required=True)
    hotel_id = fields.String()
    application_id = fields.String(required=True)

    @validates_schema
    def validate_hotel_info(self, data, **kwargs):
        if data.get('hotel_id'):
            property_service.get_property(data.get('hotel_id'))
        return data


class UserRoleCreationSchema(Schema):
    user_role_details = fields.Nested(UserRoleDetailSchema, many=True)


