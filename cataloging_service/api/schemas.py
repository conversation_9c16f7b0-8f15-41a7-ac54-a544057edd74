import json
import re
from datetime import datetime
from decimal import Decimal

from flask import current_app
from marshmallow import Schema, fields, schema, validates_schema, post_load, post_dump, ValidationError
from marshmallow.validate import OneOf, Range
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from cataloging_service.constants import constants, error_codes
from cataloging_service.constants.model_choices import HygieneShieldNameChoices, MenuTypeChoices
from cataloging_service.constants.property_config import CurrencyConfig
from cataloging_service.controllers.serializer import Policy
from cataloging_service.exceptions import ValidationException
from cataloging_service.extensions import ma
from cataloging_service.common.schema_registry import swag_schema
from cataloging_service.models import (
    GuestType, Location, Country, State, MicroMarket, City, Cluster, Locality, Cuisine, Property, PropertyDetail,
    BankDetail, GuestFacingProcess, Owner, Description, NeighbouringPlace, PropertyAmenity, AmenityPublicWashroom,
    AmenityElevator, AmenityParking, AmenityPrivateCab, AmenitySwimmingPool, AmenityGym, AmenitySpa, AmenityLaundry,
    AmenityBreakfast, AmenityPayment, AmenityDisableFriendly, Restaurant, Bar, BanquetHall, RoomAmenity, AmenityHeater,
    AmenityTwinBed, AmenityIntercom, AmenityHotWater, AmenityTV, AmenityAC, AmenityStove, TransportStation,
    RoomTypeConfiguration, RoomType, Region, PropertyImage, CityAlias, SkuCategory, Channel, SubChannel, Application,
    Provider, Sku, PropertySku, SkuActivation, PricingPolicy, Brand, RuptubLegalEntityDetails, SellerSku,
    SellerCategory, Seller, Room, RoomRackRateModel, VariantGroup, MenuTiming, Menu, PropertyVideo)
from cataloging_service.constants.cataloging_constant import OfferingType, SkuFrequencyType, DayOfServingSku
from cataloging_service.utils import convert_time_to_hour_format


class CountrySchema(ma.Schema):
    class Meta:
        model = Country
        fields = ('id', 'name', 'iso_code')


class StateSchema(ma.Schema):
    class Meta:
        model = State
        fields = ('id', 'code', 'name')


class MicroMarketSchema(ma.Schema):
    class Meta:
        model = MicroMarket
        fields = ('id', 'name')


class CityAliasSchema(ma.Schema):
    class Meta:
        model = CityAlias
        fields = ('id', 'name')


class CitySchema(ma.Schema):
    aliases = ma.Nested(CityAliasSchema, many=True)
    country = ma.Method('get_country')

    def get_country(self, city):
        if not city.state.country:
            return None
        return CountrySchema(only=('name',)).dump(city.state.country)

    class Meta:
        model = City
        fields = ('id', 'name', 'aliases', 'latitude', 'longitude', 'country')


class RegionSchema(ma.Schema):
    class Meta:
        model = Region
        fields = ('id', 'name')


class ClusterSchema(ma.Schema):
    class Meta:
        model = Cluster
        fields = ('id', 'name')


class LocalitySchema(ma.Schema):
    class Meta:
        model = Locality
        fields = ('id', 'name', 'latitude', 'longitude')


class CuisineSchema(ma.Schema):
    class Meta:
        model = Cuisine
        fields = ('id', 'name')


class PropertyNameSchema(ma.Schema):
    old_name = ma.String()
    new_name = ma.Method('get_new_name')
    legal_name = ma.String()

    def get_new_name(self, property_object):
        return property_object.name

    class Meta:
        model = Property
        fields = ('old_name', 'new_name', 'legal_name')


class GuestTypeSchema(ma.Schema):
    class Meta:
        model = GuestType
        fields = ('id', 'type')


class LocationSchema(ma.Schema):
    city = ma.Nested(CitySchema)
    legal_city = ma.Nested(CitySchema)
    micro_market = ma.Nested(MicroMarketSchema)
    locality = ma.Nested(LocalitySchema)
    state = ma.Method('get_state')
    legal_state = ma.Method('get_legal_state')
    country = ma.Method('get_country')

    def get_state(self, location):
        if not location.city:
            return None

        return StateSchema().dump(location.city.state)

    def get_legal_state(self, location):
        if not location.legal_city:
            return None

        return StateSchema().dump(location.legal_city.state)

    def get_country(self, location):
        if not location.city.state.country:
            return None

        return CountrySchema().dump(location.city.state.country)

    class Meta:
        model = Location
        fields = (
            'id', 'latitude', 'longitude', 'pincode', 'postal_address', 'maps_link', 'micro_market', 'locality', 'city',
            'state', 'legal_address', 'legal_city', 'legal_state', 'legal_pincode', 'country')


class BankDetailSchema(ma.Schema):
    type = ma.Method('get_account_type')

    def get_account_type(self, bank_detail_object):
        return bank_detail_object.account_type

    class Meta:
        model = BankDetail
        fields = (
            'id', 'account_name', 'account_number', 'type', 'ifsc_code', 'branch_code', 'swift_code', 'bank', 'branch')


class PropertyDetailSchema(ma.Schema):
    style = ma.Method('get_property_style')
    building_type = ma.Method('get_building_style')
    bank_details = ma.Method('get_bank_details')
    previously_different_franchise = ma.Method('get_previous_franchise')
    external_provider = ma.Method('get_external_provider')
    sold_as = ma.Method('get_sold_as')
    legal_signature = ma.Method('get_complete_path')
    superhero_price_slab = ma.Method('get_superhero_price_slab')
    hygiene_shield_name = ma.Method('get_hygiene_shield_name')

    def get_bank_details(self, property_detail_object):
        if not property_detail_object.bank_detail:
            return None
        return BankDetailSchema().dump(property_detail_object.bank_detail)

    def get_property_style(self, property_detail_object):
        return property_detail_object.property_style

    def get_building_style(self, property_detail_object):
        return property_detail_object.building_style

    def get_previous_franchise(self, property_detail_object):
        return property_detail_object.previous_franchise

    def get_external_provider(self, property_detail_object):
        if not property_detail_object.ext_id:
            return None
        return dict(provider_name=property_detail_object.provider.name,
                    code=property_detail_object.provider.code,
                    provider_hotel_code=property_detail_object.provider_hotel_code)

    def get_sold_as(self, property_detail_object):
        sold_as = getattr(property_detail_object, 'sold_as', None)
        if not sold_as:
            return None
        return sold_as.value

    def get_superhero_price_slab(self, property_detail_object):
        if property_detail_object.superhero_price_slab:
            return property_detail_object.superhero_price_slab.value
        return None

    def get_complete_path(self, property_detail):
        if property_detail.legal_signature:
            cdn_host = current_app.config[constants.CONFIG_CDN_HOST]
            return "%s%s" % (cdn_host, re.sub(r'^\.', '', property_detail.legal_signature))

    def get_hygiene_shield_name(self, property_detail_object):
        if property_detail_object.hygiene_shield_name:
            return HygieneShieldNameChoices(property_detail_object.hygiene_shield_name).value
        return None

    class Meta:
        model = PropertyDetail
        fields = ('id', 'neighbourhood_type', 'neighbourhood_detail', 'style', 'style_detail', 'property_type',
                  'construction_year', 'building_type', 'floor_count', 'star_rating', 'previously_different_franchise',
                  'bank_details', 'reception_landline', 'reception_mobile', 'email', 'pan', 'tan', 'tin', 'gstin',
                  'external_provider', 'sold_as', 'legal_signature', 'navision_code', 'is_housekeeping_enabled',
                  'churn_initiation_date', 'has_lut', 'is_partner_pricing_enabled', 'superhero_price_slab',
                  'is_ds_pricing_enabled', 'hygiene_shield_name', 'is_hotel_superhero_setup_completed', 'msme_number')


class GuestFacingProcessSchema(ma.Schema):
    free_early_checkin_time = ma.Method('get_free_early_checkin')
    free_late_checkout_time = ma.Method('get_free_late_checkout')
    checkout_time = ma.Method('get_checkout_time')
    checkin_time = ma.Method('get_checkin_time')
    switch_over_time = ma.Method('get_switch_over_time')
    system_freeze_time = ma.Method('get_system_freeze_time')

    class Meta:
        model = GuestFacingProcess
        fields = ('id', 'free_early_checkin_time', 'checkout_time', 'checkin_time', 'free_late_checkout_time',
                  'early_checkin_fee', 'late_checkout_fee', 'switch_over_time', 'checkin_grace_time',
                  'checkout_grace_time', 'system_freeze_time')

    def get_free_early_checkin(self, guest_facing_process_object):
        return guest_facing_process_object.free_early_checkin.isoformat()

    def get_free_late_checkout(self, guest_facing_process_object):
        return guest_facing_process_object.free_late_checkout.isoformat()

    def get_checkout_time(self, guest_facing_process_object):
        return guest_facing_process_object.checkout_time.isoformat()

    def get_checkin_time(self, guest_facing_process_object):
        return guest_facing_process_object.checkin_time.isoformat()

    def get_switch_over_time(self, guest_facing_process_object):
        return guest_facing_process_object.switch_over_time.isoformat()

    def get_system_freeze_time(self, guest_facing_process_object):
        return guest_facing_process_object.system_freeze_time.isoformat()


class OwnerSchema(ma.Schema):
    dob = ma.Method('get_date_of_birth')

    def get_date_of_birth(self, owner_object):
        date_of_birth = getattr(owner_object, 'date_of_birth', None)
        if date_of_birth:
            return date_of_birth.isoformat()
        return date_of_birth

    class Meta:
        model = Owner
        fields = (
            'id', 'first_name', 'middle_name', 'last_name', 'gender', 'email', 'phone_number', 'dob', 'occupation',
            'education')


class DescriptionSchema(ma.Schema):
    class Meta:
        model = Description
        fields = ('id', 'property_description', 'acacia_description', 'maple_description', 'oak_description',
                  'mahogany_description', 'trilight_one', 'trilight_two', 'trilight_three')


class NeighbouringPlaceSchema(ma.Schema):
    class Meta:
        model = NeighbouringPlace
        fields = (
            'id', 'nearest_hospital', 'utility_shops', 'restaurants', 'tourist_spots', 'corporate_offices',
            'popular_malls', 'shopping_streets', 'city_centre')


class BulkPropertySchema(ma.Schema):
    name = ma.Method('get_name')
    location = ma.Nested(LocationSchema)
    launched_date = ma.Method('get_launch_date')
    policy_ids = ma.Method('get_policy_ids')
    current_business_date = fields.Date()
    churned_date = ma.Method('get_churned_date')

    uri = ma.URLFor('property_apis.get_property', property_id='<id>')

    def get_name(self, property_object):
        return PropertyNameSchema().dump(property_object)

    def get_launch_date(self, property_object):
        return property_object.launched_date.strftime("%Y-%m-%d") if property_object.launched_date else None

    def get_churned_date(self, property_object):
        return property_object.churned_date.strftime("%Y-%m-%d") if property_object.churned_date else None

    def get_policy_ids(self, property_object):
        return []

    class Meta:
        fields = ('id', 'name', 'location', 'status', 'hx_id', 'launched_date', 'uri', 'policy_ids', 'churned_date')


class TransportSchema(ma.Schema):
    class Meta:
        model = TransportStation
        fields = ('name', 'latitude', 'longitude')


class PropertyImageSchema(ma.Schema):
    room_type = ma.Method('get_room_type')
    path = ma.Method('get_complete_path')

    def get_complete_path(self, property_image):
        if property_image.path:
            cdn_host = current_app.config[constants.CONFIG_CDN_HOST]
            return "%s%s" % (cdn_host, re.sub(r'^\.', '', property_image.path))

    def get_room_type(self, property_image):
        if property_image.room_type_config:
            return property_image.room_type_config.room_type.type

    class Meta:
        model = PropertyImage
        fields = ('property_id', 'path', 'tag_description', 'sort_order', 'room_type')


class PropertyVideoSchema(ma.Schema):
    video_url = ma.Method('get_complete_path')

    def get_complete_path(self, property_video):
        if property_video.video_url:
            cdn_host = current_app.config[constants.CONFIG_CDN_HOST]
            return "%s%s" % (cdn_host, re.sub(r'^\.', '', property_video.video_url))
        return ''

    class Meta:
        model = PropertyVideo
        fields = ('property_id', 'video_url', 'tag_description', 'sort_order', 'youtube_video_url')


class CurrencySymbol(ma.Schema):
    locale = fields.Str()
    value = fields.Str()


class CurrencySchema(ma.Schema):
    name = fields.Str()
    code = fields.Str()
    currency_symbol = fields.Nested(CurrencySymbol)


class CurrencyConfigSchema(ma.Schema):
    base_currency = fields.Nested(CurrencySchema)
    payment_currencies = fields.Nested(CurrencySchema, many=True)


class PropertyConfigSchema(ma.Schema):
    currency_config = fields.Nested(CurrencyConfigSchema)


class PropertySchema(ma.Schema):
    name = ma.Method('get_name')
    suited_to = ma.Method('get_suited_to')
    location = ma.Nested(LocationSchema)
    property_details = ma.Method('get_property_details')
    guest_facing_details = ma.Method('get_guest_facing_details')
    owners = ma.Method('get_owners')
    landmarks = ma.Method('get_landmarks')
    description = ma.Nested(DescriptionSchema)
    neighbouring_places = ma.Method('get_neighbouring_places')
    transport_stations = ma.Method('get_transport_stations')
    sku_categories = ma.Method('get_sku_categories')
    room_count = ma.Method('get_room_count')
    property_images = ma.Method('get_images')
    property_videos = ma.Method('get_videos')
    amenity_summary = ma.Method('get_amenity_summary')
    room_type_configs = ma.Method('get_room_type_config')
    skus = ma.Method('get_sku')
    brands = ma.Method('get_brands')
    policies = ma.Method('get_policies')
    base_currency_code = ma.Method('get_base_currency_code')
    property_configs = ma.Method('get_property_configs')
    timezone = ma.String()
    logo = ma.String()
    country_code = ma.String()
    current_business_date = fields.Date()
    cost_center_id = fields.String()
    is_test = fields.Boolean()

    def get_name(self, property_object):
        return PropertyNameSchema().dump(property_object)

    def get_suited_to(self, property_object):
        if not property_object.guest_types:
            return []
        return GuestTypeSchema().dump(property_object.guest_types, many=True)

    def get_property_details(self, property_object):
        if not property_object.property_detail:
            return None
        return PropertyDetailSchema().dump(property_object.property_detail)

    def get_guest_facing_details(self, property_object):
        if not property_object.guest_facing_process:
            return None
        return GuestFacingProcessSchema().dump(property_object.guest_facing_process)

    def get_owners(self, property_object):
        if not property_object.ownerships:
            return []

        owners = []
        for ownership in property_object.ownerships:
            owner = OwnerSchema().dump(ownership.owner)
            owner['is_primary_owner'] = ownership.primary
            owners.append(owner)

        return owners

    def get_transport_stations(self, property_object):
        if not property_object.transport_station_assocs:
            return []

        stations = []
        for assoc in property_object.transport_station_assocs:
            station = TransportSchema().dump(assoc.transport_station)
            station['hotel_distance'] = assoc.distance_from_property
            station['hotel_direction'] = assoc.property_direction
            station['hatchback_cab_fare'] = assoc.hatchback_cab_fare
            station['sedan_cab_fare'] = assoc.sedan_cab_fare
            stations.append(station)

        return stations

    def get_neighbouring_places(self, property_object):
        if not property_object.neighbouring_place:
            return None

        return NeighbouringPlaceSchema().dump(property_object.neighbouring_place)

    def get_landmarks(self, property_object):
        property_landmarks = property_object.property_landmarks
        landmarks = []
        for property_landmark in property_landmarks:
            landmarks.append(dict(id=property_landmark.landmark.id, name=property_landmark.landmark.name,
                                  latitude=property_landmark.landmark.latitude,
                                  longitude=property_landmark.landmark.longitude, type=property_landmark.type,
                                  hotel_distance=property_landmark.distance_from_property,
                                  hotel_direction=property_landmark.property_direction,
                                  hatchback_cab_fare=property_landmark.hatchback_cab_fare,
                                  sedan_cab_fare=property_landmark.sedan_cab_fare))

        return landmarks

    def get_sku_categories(self, property_object):
        if not property_object.sku_categories:
            return None
        return SkuCategorySchema().dump(property_object.sku_categories, many=True)

    def get_room_count(self, property_object):
        if not property_object.rooms:
            return None
        return len(list(filter(lambda room: room.is_active, property_object.rooms)))

    def get_amenity_summary(self, property_object):
        amenity_summary = property_object.amenity_summary
        if not amenity_summary:
            return {}
        return json.loads(amenity_summary.summary)

    def get_room_type_config(self, property_object):
        room_configs = property_object.room_type_configurations
        if not room_configs:
            return None
        return RoomTypeConfigurationSchema().dump(room_configs, many=True)

    def get_images(self, property_object):
        images = property_object.property_images
        if not images:
            return None
        return PropertyImageSchema().dump(images, many=True)

    def get_videos(self, property_object):
        videos = property_object.property_videos
        if not videos:
            return None
        return PropertyVideoSchema().dump(videos, many=True)

    def get_sku(self, propery_object):
        property_skus = propery_object.property_skus
        skus = []
        for property_sku in property_skus:
            skus.append(dict(id=property_sku.id, property_id=property_sku.property_id,
                             sku_name=property_sku.sku.name, sku_code=property_sku.sku.code,
                             status=property_sku.status, saleable=property_sku.saleable,
                             hsn_sac=property_sku.sku.hsn_sac, is_sku_saleable=property_sku.sku.saleable,
                             default_list_price=property_sku.sku.default_list_price,
                             default_sale_price=property_sku.sku.default_sale_price,
                             sku_category_code=property_sku.sku.category.code,
                             description=property_sku.description, extra_information=property_sku.extra_information,
                             sell_separatre=property_sku.sell_separate, rack_rate=property_sku.rack_rate))
        return skus

    def get_brands(self, property):
        if not property.brands:
            return None
        return BrandSchema().dump(property.brands, many=True)

    def get_policies(self, property_object):
        policies = []
        for policy in property_object.property_detail.policies:
            policy_details = Policy().dump(policy)
            policies.append(policy_details)
        return policies

    def get_base_currency_code(self, property_object):
        return property_object.base_currency_code if property_object.base_currency_code else CurrencyType.INR.value

    def get_property_configs(self, property_object):
        if not CurrencyConfig.HOTEL_WISE_SUPPORTED_CURRENCY_MAP.get(property_object.id):
            return dict()
        return PropertyConfigSchema().dump(
            dict(currency_config=dict(CurrencyConfig.HOTEL_WISE_SUPPORTED_CURRENCY_MAP.get(property_object.id))))

    class Meta:
        model = Property
        fields = ('id', 'name', 'location', 'suited_to', 'property_details', 'guest_facing_details', 'signed_date',
                  'contractual_launch_date', 'launched_date', 'churned_date', 'owners', 'landmarks', 'description',
                  'neighbouring_places', 'hx_id', 'status', 'transport_stations', 'sku_categories', 'room_count',
                  'property_images', 'property_videos', 'amenity_summary', 'room_type_configs', 'skus', 'brands', 'policies',
                  'base_currency_code', 'property_configs', 'timezone', 'logo', 'country_code', 'current_business_date',
                  'external_hotel_id', 'is_test',)


class RoomTypeSchema(ma.Schema):
    associated_modular_sku_code = ma.Method("get_associated_modular_sku_code")

    def get_associated_modular_sku_code(self, data):
        if hasattr(data, 'associated_modular_sku_code'):
            return data.associated_modular_sku_code
        from cataloging_service.domain import service_provider
        return service_provider.sku_service.sget_associated_sku_for_room_type(data.type)

    class Meta:
        model = RoomType
        fields = ('id', 'code', 'type', 'unirate_room_type_code', 'crs_room_type_code', 'bb_room_type_code',
                  'associated_modular_sku_code')


class RoomTypeConfigurationSchema(ma.Schema):
    room_type = ma.Method("get_room_type")
    room_count = ma.Method('get_room_count')
    ext_provider_code = ma.Method('get_external_provider')

    def get_room_type(self, data):
        return RoomTypeSchema().dump(data.room_type)

    def get_room_count(self, room_config):
        return len(list(filter(lambda room: room.is_active, room_config.property_rooms)))

    def get_external_provider(self, room_type_configuration):
        return room_type_configuration.provider.code if room_type_configuration.ext_id else None

    class Meta:
        model = RoomTypeConfiguration
        fields = ('id', 'room_type', 'extra_bed', 'min_occupancy', 'max_occupancy', 'adults', 'mm_id', 'children',
                  'max_total', 'room_count', 'min_room_size', 'ext_provider_code', 'ext_room_code', 'ext_room_name',
                  'description', 'display_name', 'ext_rate_plan_code', 'ext_rate_plan_name')


class RoomSchema(ma.Schema):
    room_type = ma.Nested(RoomTypeSchema)

    class Meta:
        model = Room
        fields = ('id', 'room_number', 'room_type', 'building_number', 'floor_number', 'size', 'is_active', 'room_size',
                  'linked_room_identifier')


class PublicWashroomSchema(ma.Schema):
    class Meta:
        model = AmenityPublicWashroom
        fields = ('id', 'gender_segregated')


class ElevatorSchema(ma.Schema):
    class Meta:
        model = AmenityElevator
        fields = ('id', 'floors_accessible')


class ParkingSchema(ma.Schema):
    class Meta:
        model = AmenityParking
        fields = ('id', 'location', 'max_two_wheelers', 'max_four_wheelers', 'charges')


class PrivateCabSchema(ma.Schema):
    class Meta:
        model = AmenityPrivateCab
        fields = ('id', 'charges')


class DisableFriendlySchema(ma.Schema):
    class Meta:
        model = AmenityDisableFriendly
        fields = (
            'id', 'ramp_available', 'wheelchair_count', 'disable_friendly_room_available', 'disable_friendly_rooms')


class SwimmingPoolSchema(ma.Schema):
    class Meta:
        model = AmenitySwimmingPool
        fields = ('id', 'location', 'pool_size', 'open_time', 'close_time', 'active')


class GymSchema(ma.Schema):
    class Meta:
        model = AmenityGym
        fields = ('id', 'open_time', 'close_time', 'equipments_available', 'active')


class SpaSchema(ma.Schema):
    class Meta:
        model = AmenitySpa
        fields = ('id', 'open_time', 'close_time', 'active')


class LaundrySchema(ma.Schema):
    class Meta:
        model = AmenityLaundry
        fields = ('id', 'pickup_time', 'drop_time', 'is_external')


class BreakfastSchema(ma.Schema):
    cuisines = ma.Method('get_cuisines')

    def get_cuisines(self, breakfast):
        if not breakfast.cuisines:
            return []
        return CuisineSchema().dump(breakfast.cuisines, many=True)

    class Meta:
        model = AmenityBreakfast
        fields = ('id', 'type', 'service_area', 'non_veg', 'rotational', 'cuisines')


class PaymentSchema(ma.Schema):
    class Meta:
        model = AmenityPayment
        fields = ('id', 'amex_accepted', 'wallet_accepted')


class PropertyAmenitySchema(ma.Schema):
    public_washroom = ma.Nested(PublicWashroomSchema)
    elevator = ma.Nested(ElevatorSchema)
    parking = ma.Nested(ParkingSchema)
    private_cab = ma.Nested(PrivateCabSchema)
    disable_friendly = ma.Nested(DisableFriendlySchema)
    swimming_pool = ma.Nested(SwimmingPoolSchema)
    gym = ma.Nested(GymSchema)
    spa = ma.Nested(SpaSchema)
    laundry = ma.Nested(LaundrySchema)
    breakfast = ma.Nested(BreakfastSchema)
    payment = ma.Nested(PaymentSchema)

    class Meta:
        model = PropertyAmenity
        fields = (
            'id', 'public_washroom', 'elevator', 'lobby_ac', 'lobby_furniture', 'lobby_smoke_alarm', 'security',
            'pantry', 'cloak_room', 'travel_desk', 'room_service', 'roof_top_cafe', 'pool_table', 'pets_allowed',
            'parking', 'private_cab', 'iron_board_count', 'driver_quarters_count', 'disable_friendly', 'swimming_pool',
            'gym', 'spa', 'laundry', 'breakfast', 'payment')


class RestaurantSchema(ma.Schema):
    cuisines = ma.Method('get_cuisines')

    def get_cuisines(self, restaurant):
        if not restaurant.cuisines:
            return []

        return CuisineSchema().dump(restaurant.cuisines, many=True)

    class Meta:
        model = Restaurant
        fields = (
            'id', 'name', 'non_veg', 'cuisines', 'open_time', 'last_order_time', 'close_time', 'a_la_carte', 'buffet',
            'outside_food_allowed', 'baby_milk_served', 'baby_milk_timing', 'handwash_present', 'washroom_present',
            'egg_served', 'jain_food_served', 'room_service_start_time', 'room_service_end_time')


class BarSchema(ma.Schema):
    class Meta:
        model = Bar
        fields = ('id', 'name', 'open_time', 'last_order_time', 'close_time', 'room_start_time', 'room_end_time')


class BanquetHallSchema(ma.Schema):
    class Meta:
        model = BanquetHall
        fields = ('id', 'name', 'floor', 'capacity', 'size')


class HeaterSchema(ma.Schema):
    class Meta:
        model = AmenityHeater
        fields = ('id', 'availability', 'charges')


class TwinBedSchema(ma.Schema):
    class Meta:
        model = AmenityTwinBed
        fields = ('id', 'joinable')


class IntercomSchema(ma.Schema):
    class Meta:
        model = AmenityIntercom
        fields = ('id', 'all_rooms_connected')


class HotWaterSchema(ma.Schema):
    class Meta:
        model = AmenityHotWater
        fields = ('id', 'central_geyser', 'room_geyser', 'from_time', 'to_time')


class TVSchema(ma.Schema):
    class Meta:
        model = AmenityTV
        fields = ('id', 'vendor', 'tv_type', 'connection_type', 'size')


class ACSchema(ma.Schema):
    class Meta:
        model = AmenityAC
        fields = ('id', 'ac_type')


class StoveSchema(ma.Schema):
    class Meta:
        model = AmenityStove
        fields = ('id', 'stove_type', 'availability')


class RoomAmenitySchema(ma.Schema):
    heater = ma.Nested(HeaterSchema)
    twin_bed = ma.Nested(TwinBedSchema)
    intercom = ma.Nested(IntercomSchema)
    hot_water = ma.Nested(HotWaterSchema)
    tv = ma.Nested(TVSchema)
    ac = ma.Nested(ACSchema)
    stove = ma.Nested(StoveSchema)

    class Meta:
        model = RoomAmenity
        fields = (
            'id', 'mini_fridge', 'balcony', 'kitchenette', 'kitchenette_utensils', 'king_sized_beds',
            'queen_sized_beds', 'single_beds', 'wardrobe', 'locker_available', 'microwave', 'luggage_shelf',
            'study_table_chair', 'sofa_chair', 'coffee_table', 'other_furniture', 'smoking_room', 'bath_tub',
            'shower_curtain', 'smoke_alarm', 'shower_cabinets', 'living_room', 'dining_table', 'windows',
            'treebo_toiletries', 'fan_type', 'lock_type', 'bucket_mug', 'mosquito_repellent', 'heater', 'twin_bed',
            'intercom', 'hot_water', 'tv', 'ac', 'stove')


@swag_schema
class SkuCategorySchema(ma.Schema):
    class Meta:
        model = SkuCategory
        fields = ('name', 'hsn_sac', 'status', 'code', 'has_slab_based_taxation')

    @post_dump
    def customise_serialized_data(self, data, **kwargs):
        if data and not data['has_slab_based_taxation']:
            data['has_slab_based_taxation'] = False
        return data


@swag_schema
class SkuSchema(ma.Schema):
    sku_category_code = ma.Method('get_sku_category_code')
    profit_center_code = ma.Method('get_profit_center_code')
    rack_rate = ma.Method('get_rack_rate')
    skus = ma.Method('get_sku')
    sell_separate = ma.Boolean()
    extra_information = ma.Method('get_extra_information')

    def get_sku(self, sku):
        sku_bundles = sku.bundle
        skus = []
        for sku_bundle in sku_bundles:
            skus.append(dict(name=sku_bundle.sku.name, code=sku_bundle.sku.code,
                             saleable=sku_bundle.sku.saleable, hsn_sac=sku_bundle.sku.hsn_sac,
                             sku_count=sku_bundle.count, sku_category_code=sku_bundle.sku.category.code,
                             offering=sku_bundle.sku.offering, frequency=sku_bundle.sku.frequency))
        return skus

    def get_sku_category_code(self, sku):
        category = getattr(sku, 'category', None)
        if not category:
            return None
        return category.code

    def get_profit_center_code(self, sku):
        profit_center = getattr(sku, 'profit_center', None)
        if not profit_center:
            return None
        return profit_center.code

    def get_rack_rate(self, sku):
        if hasattr(sku, 'rack_rate'):
            return sku.rack_rate

    def get_extra_information(self, sku):
        return getattr(sku, 'extra_information', None)

    class Meta:
        model = Sku
        fields = ('code', 'name', 'hsn_sac', 'description', 'skus', 'rack_rate',
                  'saleable', 'sku_category_code', 'chargeable_per_occupant', 'default_list_price', 'tax_type',
                  'sku_type', 'is_modular', 'display_name', 'default_sale_price', 'offering', 'frequency',
                  'is_property_inclusion', 'tax_at_room_rate', 'extra_information', 'sell_separate',
                  'profit_center_code', 'property_id')


class SellerSkuSchema(ma.Schema):
    seller = ma.Method('get_seller_detials')

    def get_seller_detials(self, seller_sku_object):
        if not seller_sku_object.seller:
            return None
        return SellerSchema().dump(seller_sku_object.seller)

    class Meta:
        model = SellerSku
        fields = ('id', 'sku_id', 'is_active', 'seller_id', 'sku_category_code', 'name',
                  'display_name', 'images', 'description', 'saleable', 'menu_category_id', 'pretax_price',
                  'seller_category_id', 'seller')


class SellerCategorySchema(ma.Schema):
    class Meta:
        model = SellerCategory
        fields = ('name', 'code', 'is_active')


class SellerSchema(ma.Schema):
    class Meta:
        model = Seller
        fields = ('name', 'gstin', 'legal_name', 'legal_address', 'legal_city',
                  'legal_pincode', 'phone_number', 'seller_category_id', 'property_id')


class MultiplePropertySkuSchema(ma.Schema):
    property_id = ma.String()
    skus = ma.Nested(SkuSchema, many=True)


class PropertySkuSchema(ma.Schema):
    sku = ma.Nested(SkuSchema)
    description = ma.String()
    extra_information = ma.Method('get_extra_information')
    sell_separate = ma.Boolean()

    def get_extra_information(self, property_sku):
        return property_sku.extra_information

    class Meta:
        model = PropertySku
        fields = ('property_id', 'sku_id', 'sku', 'status', 'saleable', 'display_name', 'rack_rate',
                  'description', 'extra_information', 'sell_separate')


class PropertySkuRoomMappingSchema(ma.Schema):
    room_type = ma.String()
    room_config = ma.String()
    room_code = ma.String()
    property_sku = ma.Nested(PropertySkuSchema)


class CustomPropertySkuSchema(ma.Schema):
    property_id = ma.String(required=True)
    property_sku_room_mapping = ma.Nested(PropertySkuRoomMappingSchema, many=True)


class SkuActivationSchema(ma.Schema):
    sku = ma.Method('get_sku')
    service = ma.Method('get_service')

    def get_sku(self, sku_activation_obj):
        return sku_activation_obj.sku.code

    def get_service(self, sku_activation_obj):
        return sku_activation_obj.param.value

    class Meta:
        model = SkuActivation
        fields = ('property_id', 'sku', 'service')


class SubChannelSchema(ma.Schema):
    pricing_policies = ma.Method('get_pricing_policies')

    def get_pricing_policies(self, sub_channel):
        policies = []
        for pricing_mapping in sub_channel.pricing_mapping:
            pricing = pricing_mapping.pricing
            policies.append(dict(code=pricing.code, name=pricing.name, display_name=pricing.display_name,
                                 is_default=pricing.is_default, description=pricing.description,
                                 policy_status=pricing.status, channel_id=pricing_mapping.channel_id,
                                 mapping_status=pricing_mapping.status,
                                 sub_channel_id=pricing_mapping.sub_channel_id))
        return policies

    class Meta:
        model = SubChannel
        fields = ('id', 'name', 'description', 'status', 'channel_id', 'pricing_policies', 'priority')


class ApplicationSchema(ma.Schema):
    class Meta:
        model = Application
        fields = ('id', 'name', 'description', 'status', 'channel_id')


class ChannelSchema(ma.Schema):
    sub_channels = ma.Method('get_sub_channels')
    applications = ma.Method('get_applications')
    pricing_policies = ma.Method('get_pricing_policies')

    def get_sub_channels(self, channel):
        return SubChannelSchema().dump(channel.sub_channels, many=True)

    def get_applications(self, channel):
        return ApplicationSchema().dump(channel.applications, many=True)

    def get_pricing_policies(self, channel):
        # TODO: This is duplicated data, already available in sub_channels. Can be removed
        policies = []
        for subchannel in channel.sub_channels:
            for pricing_mapping in subchannel.pricing_mapping:
                pricing = pricing_mapping.pricing
                policies.append(dict(code=pricing.code, name=pricing.name, display_name=pricing.display_name,
                                     is_default=pricing.is_default, description=pricing.description,
                                     policy_status=pricing.status, channel_id=pricing_mapping.channel_id,
                                     mapping_status=pricing_mapping.status,
                                     sub_channel_id=pricing_mapping.sub_channel_id))
        return policies

    class Meta:
        model = Channel
        fields = ('id', 'name', 'description', 'status', 'sub_channels', 'applications', 'pricing_policies', 'priority')


class PricingPolicySchema(ma.Schema):
    class Meta:
        model = PricingPolicy
        fields = ('code', 'name', 'display_name', 'is_default', 'description', 'status')


class ProviderSchema(ma.Schema):
    """
    No clarity on one-to-one relationship on rate plans
    """
    rate_plan = ma.Method('get_rate_plan')
    room_mappings_provider = ma.Method('get_room_mappings_provider')
    room_mappings_property = ma.Method('get_room_mappings_property')
    brands = ma.Method('get_brands')

    def get_rate_plan(self, provider):
        rate_plans = provider.rate_plan
        plans = []
        for rate_plan in rate_plans:
            treebo_plan = rate_plan.treebo_plan.plan if rate_plan.treebo_plan else None
            plans.append(dict(plan=rate_plan.plan, treebo_plan=treebo_plan))
        return plans

    def get_room_mappings_provider(self, provider):
        room_mappings = provider.room_mappings
        mappings = []
        for room_mapping in room_mappings:
            mappings.append(dict(treebo_type=room_mapping.room_type.type,
                                 treebo_code=room_mapping.room_type.code,
                                 name=room_mapping.ext_room_name,
                                 code=room_mapping.ext_room_code))

        return mappings

    def get_room_mappings_property(self, provider):
        from cataloging_service.domain import service_provider
        return service_provider.provider_service.sget_all_ext_room_configs_property(provider)

    def get_brands(self, provider):
        if not provider.brands:
            return None
        return BrandSchema().dump(provider.brands, many=True)

    class Meta:
        model = Provider
        fields = ('name', 'code', 'status', 'rate_plan', 'room_mappings_provider', 'room_mappings_property',
                  'brands')


class BrandSchema(ma.Schema):
    class Meta:
        model = Brand
        fields = ('code', 'name', 'display_name', 'legal_name', 'short_description', 'short_description', 'status',
                  'logo', 'color', 'brand_code')


class RuptubLegalEntityDetailsSchema(ma.Schema):
    state = ma.Method('get_legal_entities')

    def get_legal_entities(self, legal_entity):
        state = dict(
            id=legal_entity.state.id,
            legal_code=legal_entity.state.code,
            name=legal_entity.state.name
        )
        return state

    class Meta:
        model = RuptubLegalEntityDetails
        fields = ('id', 'gstin', 'date_of_registration', 'address_line_1', 'address_line_2',
                  'address_city', 'address_pincode', 'legal_name', 'state')


class HotelOwnerResponseSchema(Schema):
    first_name = fields.String()
    last_name = fields.String()
    email = fields.String()
    phone_number = fields.String()


class GetPropertiesV2RequestSchema(Schema):
    property_id = fields.String(required=False)
    modified_date = fields.String(required=False)
    status = fields.String(required=False)
    from_cache = fields.String(required=False)

    @validates_schema
    def validate_data(self, data, **kwargs):
        if data.get('modified_date'):
            if not dateutils.validate_date_format(data['modified_date']):
                raise ValidationException(
                    error_codes.INVALID_REQUEST_DATA,
                    'modified_date must be provided in ymd format'
                )
        return data

    @post_load
    def load_data(self, data, **kwargs):
        if data.get('modified_date'):
            data['modified_date'] = dateutils.localize_datetime(
                datetime.strptime(data['modified_date'], "%Y-%m-%d")
            )

        if data.get('property_id'):
            data['property_id'] = data['property_id'].split(',')

        if data.get('status'):
            data['status'] = data['status'].split(',')
        return data


class MenuTimingSchema(ma.Schema):
    menu_timing_id = ma.Integer(attribute='id')
    days = ma.Method('get_days')
    start_time = fields.String()
    end_time = fields.String()

    def get_days(self, menu_timing):
        return json.loads(menu_timing.days.decode())


class MenuCategorySchema(ma.Schema):
    menu_category_id = ma.Integer(attribute='id')
    name = fields.String()
    display_order = fields.Integer()


@swag_schema
class MenuSchema(ma.Schema):
    menu_id = ma.Integer(attribute='id')
    name = fields.String()
    code = fields.String()
    display_name = fields.String()
    description = fields.String()
    menu_types = ma.Method('get_menu_types')

    def get_menu_types(self, menu):
        return [MenuTypeChoices(menu_type.lower()).value for menu_type in menu.menu_types]


class MenuComboCategorySchema(ma.Schema):
    menu_combo_category_id = ma.Integer(attribute='id')
    menu_combo_id = fields.Integer()
    menu_category = ma.Nested(MenuCategorySchema)


class MenuItemCategorySchema(ma.Schema):
    menu_item_category_id = ma.Integer(attribute='id')
    menu_item_id = fields.Integer()
    menu_category = ma.Nested(MenuCategorySchema)


class Variant(Schema):
    variant_id = ma.Integer(attribute='id')
    name = fields.String()
    display_order = fields.Integer()
    variant_group_id = fields.Integer()


@swag_schema
class ItemCustomisationSchema(Schema):
    item_customisation_id = ma.Integer(attribute='id')
    delta_price = fields.Decimal()
    cost = fields.Decimal()
    name = ma.String(attribute='variant.name')
    variant_id = fields.Integer()
    variant_group_id = ma.Integer(attribute='variant.variant_group.id')


class ItemVariantVariantSchema(Schema):
    variant_id = ma.Integer(attribute='variant.id')
    name = fields.String(attribute='variant.name')
    display_order = fields.Integer(attribute='variant.display_order')
    variant_group_id = fields.Integer(attribute='variant.variant_group_id')


class ItemVariantSchema(Schema):
    item_variant_id = ma.Integer(attribute='id')
    name = fields.String()
    display_order = fields.Integer()
    pre_tax_price = fields.Decimal(as_string=True)
    cost = fields.Decimal(as_string=True)
    sku_id = fields.Integer()
    item_id = fields.Integer()
    variants = ma.Nested(ItemVariantVariantSchema, many=True)
    item_customisations = ma.Nested(ItemCustomisationSchema, many=True)
    sku_category_code = fields.String()


class PrepTimeSchema(Schema):
    prep_time = ma.Method('format_prep_time')

    def format_prep_time(self, item):
        return convert_time_to_hour_format(item.prep_time)


class BaseItemModelSchema(PrepTimeSchema):
    item_id = ma.Integer(attribute='id')
    name = fields.String()
    description = fields.String()
    sku_category_code = fields.String()
    display_name = fields.String()
    active = fields.Boolean()
    sold_out = fields.Boolean()
    print_name = fields.String()
    kitchen_id = fields.Integer()
    use_as_side = fields.Boolean()
    contains_alcohol = fields.Boolean()
    pre_tax_price = fields.Decimal(as_string=True)
    tax_value = fields.Decimal(as_string=True)
    cost = fields.Decimal(as_string=True)
    sku_id = fields.Integer()
    food_type = fields.String()
    image = fields.String()
    allergen_info = fields.String()
    calorie_info = fields.String()
    seller_id = fields.String()
    code = fields.String()


class SideItemSchema(Schema):
    side_item_id = ma.Integer(attribute='id')
    item = ma.Nested(BaseItemModelSchema, attribute='side_item')
    item_variant = ma.Nested(ItemVariantSchema, attribute='side_item_variant')


class MenuItemItemVariantSchema(ma.Schema):
    menu_item_item_variant_id = ma.Integer(attribute='id')
    menu_item_id = fields.Integer()
    item_variant_id = fields.Integer()
    name = ma.Method('get_item_variant_name')
    pre_tax_price = ma.Method('get_item_variant_pre_tax_price')

    def get_item_variant_name(self, menu_item_item_variant):
        return menu_item_item_variant.item_variant.name

    def get_item_variant_pre_tax_price(self, menu_item_item_variant):
        return menu_item_item_variant.item_variant.pre_tax_price


class MenuItemSideItemSchema(ma.Schema):
    menu_item_side_item_id = ma.Integer(attribute='id')
    menu_item_id = fields.Integer()
    side_item_id = fields.Integer()


class MenuItemItemCustomisationSchema(ma.Schema):
    menu_item_item_customisation_id = ma.Integer(attribute='id')
    menu_item_id = fields.Integer()
    item_customisation_id = fields.Integer()


@swag_schema
class VariantGroupSchema(Schema):
    variant_group_id = ma.Integer(attribute='id')
    display_name = fields.String()
    name = fields.String()
    can_select_multiple = fields.Boolean()
    can_select_quantity = fields.Boolean()
    minimum_selectable_quantity = fields.Integer()
    maximum_selectable_quantity = fields.Integer()
    is_customisation = fields.Boolean()
    variants = ma.Nested(Variant, many=True)


@swag_schema
class ItemSchema(BaseItemModelSchema):
    side_items = ma.Nested(SideItemSchema, many=True)
    variant_group_ids = fields.List(fields.Integer(attribute="id"))
    customisation_group_ids = fields.List(fields.Integer(attribute="id"))


class ItemMessageSchema(ItemSchema):
    is_deleted = fields.Boolean()
    seller_id = fields.String()


@swag_schema
class ItemVariantShortSchema(Schema):
    item_variant_id = ma.Integer(attribute='id')
    name = fields.String()
    pre_tax_price = fields.Decimal(as_string=True)
    cost = fields.Decimal(as_string=True)


@swag_schema
class ItemListSchema(Schema):
    item_id = ma.Integer(attribute='id')
    name = fields.String()
    code = fields.String()
    active = fields.Boolean()
    description = fields.String()
    cost = fields.Decimal(as_string=True)
    pre_tax_price = fields.Decimal(as_string=True)
    display_name = fields.String()
    seller_id = fields.String()
    sold_out = fields.Boolean()
    food_type = fields.String()
    use_as_side = fields.Boolean()
    kitchen_id = fields.Integer()


class MenuItemSummarySchema(Schema):
    menu_item_id = ma.Integer(attribute='id')
    item_id = ma.Integer()
    item = ma.Nested(ItemListSchema)


@swag_schema
class MenuItemSchema(MenuItemSummarySchema):
    sold_out = fields.Boolean()
    display_order = fields.Integer()
    item = ma.Nested(ItemSchema)
    item_variant = ma.Nested(ItemVariantSchema)
    menu_item_categories = ma.Nested(MenuItemCategorySchema, many=True)
    menu_item_item_variants = ma.Nested(MenuItemItemVariantSchema, many=True)
    menu_item_side_items = ma.Nested(MenuItemSideItemSchema, many=True)
    menu_item_item_customisations = ma.Nested(MenuItemItemCustomisationSchema, many=True)


class MenuWithMenuTimingSchema(MenuSchema):
    menu_timings = ma.Nested(MenuTimingSchema, many=True)


@swag_schema
class MenuItemListSchema(Schema):
    menu_item_id = ma.Integer(attribute='id')
    sold_out = fields.Boolean()
    display_order = fields.Integer()
    item = ma.Nested(ItemSchema)
    item_variant = ma.Nested(ItemVariantSchema)
    menu_item_categories = ma.Nested(MenuItemCategorySchema, many=True)
    menu_item_item_variants = ma.Nested(MenuItemItemVariantSchema, many=True)
    menu_item_side_items = ma.Nested(MenuItemSideItemSchema, many=True)
    menu_item_item_customisations = ma.Nested(MenuItemItemCustomisationSchema, many=True)
    menu = ma.Nested(MenuWithMenuTimingSchema)


class MenuCategoryItemCountSchema(ma.Schema):
    menu_category_id = ma.Integer(attribute='id')
    item_count = ma.Method('get_item_count')
    name = fields.String()
    display_order = fields.Integer()

    def get_item_count(self, menu_category):
        item_counter = 0
        for menu_item in menu_category.menu_item_categories:
            item_counter += 1

        return item_counter


class ComboItemSchema(Schema):
    combo_item_id = ma.Integer(attribute='id')
    item = ma.Nested(BaseItemModelSchema)
    item_variant = ma.Nested(ItemVariantSchema)


@swag_schema
class ComboSchema(PrepTimeSchema):
    combo_id = ma.Integer(attribute='id')
    name = fields.String()
    code = fields.String()
    description = fields.String()
    display_name = fields.String()
    use_as_side = fields.Boolean()
    image = fields.String()
    allergen_info = fields.String()
    calorie_info = fields.String()
    prep_time = fields.String()
    contains_alcohol = fields.Boolean()
    pre_tax_price = fields.Decimal(as_string=True)
    cost = fields.Decimal(as_string=True)
    sku_id = fields.Integer()
    active = ma.Method('is_active')
    sold_out = ma.Method('is_sold_out')
    food_type = fields.String()

    def is_active(self, combo):
        if not combo.combo_items:
            return combo.active
        else:
            active_list = [combo_item.item.active for combo_item in combo.combo_items]
            active_list.append(combo.active)

            return False not in active_list

    def is_sold_out(self, combo):
        if not combo.combo_items:
            return combo.sold_out
        else:
            sold_out_list = [combo_item.item.sold_out for combo_item in combo.combo_items]
            sold_out_list.append(combo.sold_out)

            return True in sold_out_list


@swag_schema
class ComboDetailSchema(ComboSchema):
    use_as_side = fields.Boolean()
    sku_category_code = fields.String()
    combo_items = ma.Nested(ComboItemSchema, many=True)


class MenuComboSummarySchema(Schema):
    menu_combo_id = ma.Integer(attribute='id')
    combo_id = ma.Integer()
    display_order = fields.Integer()
    combo = ma.Nested(ComboSchema)


class MenuListWithMenuItemsAndCategoriesSchema(MenuSchema):
    menu_categories = ma.Nested(MenuCategoryItemCountSchema, many=True)
    menu_items = ma.Nested(MenuItemSummarySchema, many=True)
    menu_combos = ma.Nested(MenuComboSummarySchema, many=True)


@swag_schema
class MenuComboSchema(Schema):
    menu_combo_id = ma.Integer(attribute='id')
    sold_out = fields.Boolean()
    display_order = fields.Integer()
    combo = ma.Nested(ComboDetailSchema)
    menu_combo_categories = ma.Nested(MenuComboCategorySchema, many=True)


@swag_schema
class MenuDetailSchema(MenuListWithMenuItemsAndCategoriesSchema):
    menu_timings = ma.Nested(MenuTimingSchema, many=True)
    menu_categories = ma.Nested(MenuCategorySchema, many=True)
    menu_items = ma.Nested(MenuItemSchema, many=True)
    menu_combos = ma.Nested(MenuComboSchema, many=True)


class MenuMessageSchema(MenuDetailSchema):
    seller_id = fields.String()
    is_deleted = fields.Boolean()


class ComboMessageSchema(ComboDetailSchema):
    is_deleted = fields.Boolean()
    seller_id = fields.String()


@swag_schema
class RoomRackRateSchema(ma.Schema):
    room_type = ma.Method('get_room_type')
    room_type_name = ma.Method('get_room_type_name')

    class Meta:
        model = RoomRackRateModel
        fields = ('room_rack_rate_id', 'property_id', 'room_type', 'adult_count', 'rack_rate', 'room_type_name')

    def get_room_type(self, room_rack_rate):
        return room_rack_rate.room_type.code

    def get_room_type_name(self, room_rack_rate):
        return room_rack_rate.room_type.type


@swag_schema
class UpdateRoomRackRateSchema(ma.Schema):
    rack_rate = fields.Decimal(required=True, validate=[Range(min=Decimal('0'), error="Rack Rate cannot be negative")])


class SkuOfferingSchema(ma.Schema):
    """
    Sku Offering Schema
    """
    offering_type = fields.String(
        validate=OneOf([OfferingType.ROOM.value, OfferingType.GUEST.value, OfferingType.BOOKING.value,
                        OfferingType.GUEST.value, OfferingType.ADULT.value, OfferingType.CHILD.value,
                        OfferingType.FIX.value])
    )
    offered_quantity = fields.Integer(required=True)


class PostingRuleSchema(ma.Schema):
    """
    Posting Rule Schema
    """
    days_of_week = fields.List(fields.String(), required=False)


class SkuFrequencySchema(ma.Schema):
    """
    Sku Frequency Schema
    """
    frequency_type = fields.String(
        validate=OneOf([SkuFrequencyType.DAILY.value, SkuFrequencyType.DURING_THE_FULL_STAY.value,
                        SkuFrequencyType.ONCE.value, SkuFrequencyType.EVERY_NIGHT_EXCEPT_CHECKIN.value,
                        SkuFrequencyType.DAYS_OF_WEEK.value])
    )
    count = fields.Integer(required=True)
    day_of_serving = fields.String(
        validate=OneOf([
            DayOfServingSku.CHECKIN.value,
            DayOfServingSku.CHECKOUT.value,
            DayOfServingSku.GUEST_PREFERENCE.value,
            DayOfServingSku.LAST_NIGHT.value,
            DayOfServingSku.POSTING_RULE_BASED.value
        ]),
        required=False
    )
    posting_rule = fields.Nested(PostingRuleSchema, required=False)


@swag_schema
class NewSkuSchema(ma.Schema):
    """
    New sku schema
    """
    name = fields.String(required=True)
    category_code = fields.String(required=True)
    rack_rate = fields.Decimal(required=True, validate=[Range(min=Decimal('0'), error="Price cannot be negative")])
    hsn_sac = fields.String(required=True)
    offering = fields.Nested(SkuOfferingSchema, required=True)
    frequency = fields.Nested(SkuFrequencySchema, required=True)
    is_property_inclusion = fields.Boolean()
    description = fields.String(required=False, allow_none=True)
    extra_information = fields.Dict(required=False, allow_none=True)
    sell_separate = fields.Boolean(required=False, allow_none=True)

    @post_load
    def load_data(self, data, **kwargs):
        if data.get('sell_separate') is None:
            data['sell_separate'] = True
        return data


@swag_schema
class UpdateSkuUnderPropertySchema(ma.Schema):
    rack_rate = fields.Decimal()
    description = fields.String(required=False, allow_none=True)
    extra_information = fields.Dict(required=False, allow_none=True)
    sell_separate = fields.Boolean(required=False, allow_none=True)
    frequency = fields.Dict(required=False, allow_none=True)
    offering = fields.Dict(required=False, allow_none=True)

    @validates_schema
    def validate_data(self, data, **kwargs):
        if data.get('rack_rate') and data['rack_rate'] < Decimal('0'):
            raise ValidationError("Price cannot be negative")
        return data


class TableSeatSchema(ma.Schema):
    seat_id = ma.Integer(attribute="id")
    seat_number = ma.Integer()


class RestaurantTableShortSchema(ma.Schema):
    table_id = ma.Integer(attribute="id")
    seller_id = fields.String()
    name = fields.String()
    table_number = fields.Integer()
    is_deleted = fields.Boolean()


@swag_schema
class RestaurantTableSchema(RestaurantTableShortSchema):
    area_id = ma.Integer()
    number_of_seats = ma.Method('get_seat_count', many=True)
    status_updated_at = fields.DateTime()
    current_status = fields.String()
    x_coordinate = fields.Integer()
    y_coordinate = fields.Integer()
    width = fields.Integer()
    height = fields.Integer()
    seats = fields.Nested(TableSeatSchema, many=True)

    def get_seat_count(self, restraunt_table):
        return len(restraunt_table.seats)


@swag_schema
class RestaurantAreaSchema(ma.Schema):
    area_id = ma.Integer(attribute="id")
    seller_id = fields.String()
    name = fields.String(required=True)
    display_order = fields.Integer()
    tables_count = ma.Method('get_table_count', many=True)

    def get_table_count(self, restaurant_area):
        return len(restaurant_area.tables)


@swag_schema
class RestaurantAreaDetailSchema(ma.Schema):
    area_id = ma.Integer(attribute="id")
    seller_id = fields.String()
    name = fields.String()
    display_order = fields.Integer()
    tables = fields.Nested(RestaurantTableSchema, many=True)

class PrinterConfigSchema(ma.Schema):
    ip = fields.String()
    printer_id = fields.String()
    port = fields.String()

@swag_schema
class KitchenSchema(ma.Schema):
    kitchen_id = ma.Integer(attribute="id")
    name = fields.String()
    config = fields.Nested(PrinterConfigSchema, many=True)


@swag_schema
class SkuSchemaV2(Schema):
    code = fields.String()
    sku_category_code = fields.String()
    name = fields.String()
    display_name = fields.String()
    saleable = fields.Boolean()
    rack_rate = fields.Decimal()
    offering = fields.Nested(SkuOfferingSchema)
    frequency = fields.Nested(SkuFrequencySchema)
    default_sale_price = fields.Decimal()
    default_list_price = fields.Decimal()
    is_property_inclusion = fields.Boolean()
    tax_at_room_rate = fields.Boolean()


class PropertyVideoMessageSchema(Schema):
    property_id = fields.String()
    youtube_video_url = fields.String()


class TestPropertyIdsResponseSchema(Schema):
    property_ids = fields.List(fields.Str(), allow_none=True)
