from typing import Optional

from object_registry import register_instance
from cataloging_service.domain.entities.templates.profit_center_template import SkuProfitCenterTemplateMappingEntity
from cataloging_service.infrastructure.repositories.sku_profit_center_template import SkuProfitCenterTemplateRepository


@register_instance(dependencies=[SkuProfitCenterTemplateRepository])
class SkuDepartmentTemplateService:
    """Service for department template business logic"""

    def __init__(self, repository: SkuProfitCenterTemplateRepository):
        self.repository: SkuProfitCenterTemplateRepository = repository

    def get_department_template_by_brand_id(self, brand_id: int) -> Optional[SkuProfitCenterTemplateMappingEntity]:
        """Get department template by ID"""
        return self.repository.get_by_brand_id(brand_id)
