import logging


from cataloging_service.domain import service_provider
from cataloging_service.domain.services.template.brand_department_template_service import (
    BrandDepartmentTemplateService,
)
from cataloging_service.domain.services.transactions.transaction_default_mapping_service import (
    TransactionDefaultMappingService,
)

from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.schemas.property_onboarding import (
    PropertyOnboardingRequestSchema,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        PropertyDepartmentService,
        TransactionDefaultMappingService,
        BrandDepartmentTemplateService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service: DepartmentTemplateService,
        profit_center_template_service: ProfitCenterTemplateService,
        property_department_service: PropertyDepartmentService,
        transaction_default_mapping_service: TransactionDefaultMappingService,
        brand_department_template_service: BrandDepartmentTemplateService,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.property_department_service = property_department_service
        self.transaction_default_mapping_service = transaction_default_mapping_service
        self.brand_department_template_service = brand_department_template_service
        self.property_service = service_provider.property_service
        self.meta_service = service_provider.meta_service
        self.seller_service = service_provider.seller_service
        self.sku_service = service_provider.sku_service

    def onboard_property(self, request: PropertyOnboardingRequestSchema) -> None:
        try:
            # Step 1: Validate property
            property_obj = self._validate_property(request.property_id)

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                self._create_departments_from_templates(
                    brand_id=request.brand_id, property_id=request.property_id
                )

            # Step 3: Create profit centers from templates
            # Profit centers are Sellers at property level
            if request.auto_create_profit_centers:
                self._create_sellers_from_profit_center_templates(
                    property_id=request.property_id,
                    brand_id=request.brand_id,
                    property_obj=property_obj,
                )

            # Step 4: Auto-create property SKUs
            if request.auto_create_property_skus:
                self._auto_create_property_skus(
                    property_id=request.property_id,
                )

            # # Step 5: Auto-create seller SKUs
            if request.auto_create_seller_skus:
                self._auto_create_seller_skus(property_id=request.property_id)

        except Exception as e:
            raise e

    def _validate_property(self, property_id: str):
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, brand_id: int, property_id: str
    ) -> bool:
        try:
            department_templates = (
                self.department_template_service.get_department_templates_by_brand(
                    brand_id=brand_id
                )
            )
            if not department_templates:
                return False

            for department_template in department_templates:
                department_entity = PropertyDepartmentEntity(
                    property_id=property_id,
                    code=department_template.code,
                    name=department_template.name,
                    parent_id=department_template.parent_code,
                    description=department_template.description,
                    financial_code=department_template.financial_code,
                    is_active=department_template.is_active,
                    is_custom=False,
                    template_code=department_template.code,
                )
                self.property_department_service.create_department(department_entity)
            return True
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            logger.error(error_msg)
            raise e

    def _create_sellers_from_profit_center_templates(
        self, property_id: str, brand_id: int, property_obj
    ) -> bool:
        try:
            profit_center_templates = (
                self.profit_center_template_service.get_auto_create_templates(
                    brand_id=brand_id
                )
            )

            if not profit_center_templates:
                logger.info(f"No profit center templates found for brand {brand_id}")
                return False

            for profit_center_template in profit_center_templates:
                department_id = self.property_department_service.get_department_id_from_template_code(
                    property_id, profit_center_template.department_template_code
                )
                default_seller_category_id = 1
                default_city_id = 1
                seller_id = self._generate_seller_id(
                    property_id, profit_center_template.code
                )
                seller = SellerEntity(
                    seller_id=seller_id,
                    name=profit_center_template.name,
                    property_id=property_id,
                    department_id=department_id,
                    seller_category_id=default_seller_category_id,
                    city_id=default_city_id,
                    status="ACTIVE",
                    timezone="Asia/Kolkata",
                    base_currency_code=property_obj.base_currency_code,
                    current_business_date=property_obj.current_business_date,
                    created_from_template_code=profit_center_template.code,
                    is_auto_created=True,
                    legal_name=property_obj.legal_name,
                )
                self.seller_service.create_seller_for_department(seller)
                logger.info(
                    f"Created profit center '{profit_center_template.name}' from template {profit_center_template.code}"
                )
            return True

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            logger.error(error_msg)
            raise e

    def _generate_seller_id(self, property_id: str, template_code: str) -> str:
        seller_id = f"{property_id}_{template_code}"
        return seller_id

    def _auto_create_property_skus(self, property_id: str) -> None:
        try:
            departments = self.property_department_service.get_departments_by_property(
                property_id=property_id
            )
            for department in departments:
                self.property_service.auto_create_sku_for_given_properties(
                    ids=[property_id], department_id=department.id
                )
        except Exception as e:
            error_msg = f"Failed to auto-create property SKUs: {str(e)}"
            logger.error(error_msg)
            raise

    def _auto_create_seller_skus(self, property_id: str) -> bool:
        try:
            sellers = self.seller_service.get_sellers(property_id=property_id)
            if not sellers:
                return False

            seller_ids = [seller.seller_id for seller in sellers]

            department_ids = (
                self.property_department_service.get_departments_by_property(
                    property_id=property_id
                )
            )
            for department in department_ids:
                self._create_seller_skus_with_department(
                    property_id, seller_ids, department.id
                )
            return True

        except Exception as e:
            error_msg = f"Failed to auto-create seller SKUs: {str(e)}"
            logger.error(error_msg)
            raise e

    def _create_seller_skus_with_department(
        self, property_id: str, seller_ids: list, department_id: int
    ):
        property_skus = self.property_service.get_property_skus_by_property_id(
            property_id
        )
        if not property_skus:
            return False
        for seller_id in seller_ids:
            for property_sku in property_skus:
                try:
                    existing_seller_sku = service_provider.seller_service.seller_sku_repository.get_seller_sku(
                        seller_id, property_sku.sku_id
                    )
                    if existing_seller_sku:
                        continue
                except Exception as e:
                    logger.debug(
                        f"Error checking existing seller SKU for seller {seller_id}, SKU {property_sku.sku_id}: {e}"
                    )
                    sku_dict = {
                        "sku_category_code": (
                            property_sku.sku.category.code
                            if property_sku.sku.category
                            else "default"
                        ),
                        "name": property_sku.sku.name,
                        "display_name": property_sku.display_name
                        or property_sku.sku.name,
                    }
                    self.sku_service._create_new_seller_sku(
                        sku_dict, property_sku.sku, seller_id, department_id
                    )
                    logger.info(
                        f"Created seller SKU for seller {seller_id}, SKU {property_sku.sku.name} with department {department_id}"
                    )
        return True
