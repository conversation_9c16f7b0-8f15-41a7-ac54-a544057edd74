from typing import List, Optional
from sqlalchemy.exc import IntegrityError
from cataloging_service.constants import error_codes
from cataloging_service.exceptions import ValidationException
from object_registry import register_instance
from cataloging_service.domain.entities.properties.property_department import PropertyDepartmentEntity
from cataloging_service.infrastructure.repositories.property_department_repository import PropertyDepartmentRepository

@register_instance(dependencies=[PropertyDepartmentRepository])
class PropertyDepartmentService:
    """Service for property department business logic"""

    def __init__(self, repository: PropertyDepartmentRepository):
        self.repository: PropertyDepartmentRepository = repository

    def create_department(self, entity: PropertyDepartmentEntity) -> PropertyDepartmentEntity:
        """Create a new property department with business validation"""
        # Business rule: Check for duplicate code within property
        if self.repository.exists_by_property_and_code(entity.property_id, entity.code):
            raise ValidationException(error_code=error_codes.INVALID_REQUEST_DATA,
                                      error_message=f"Department with code '{entity.code}' already exists for property {entity.property_id}"
            )
        try:
            return self.repository.create(entity)
        except IntegrityError as e:
            error_str = str(e.orig)
            if "property_id" in error_str:
                raise ValidationException(
                    error_code=error_codes.INVALID_REQUEST_DATA,
                    error_message=f"Property with id '{entity.property_id}' does not exist"
                )
            elif "parent_id" in error_str:
                raise ValidationException(
                    error_code=error_codes.INVALID_REQUEST_DATA,
                    error_message=f"Parent department with id '{entity.parent_id}' does not exist"
                )
        raise

    def update_department(self, entity: PropertyDepartmentEntity) -> PropertyDepartmentEntity:
        """Update property department with business validation"""
        # Business rule: Check for duplicate code within property (excluding current)
        if self.repository.exists_by_property_and_code(entity.property_id, entity.code, exclude_id=entity.id):
            raise ValidationException(error_code=error_codes.INVALID_REQUEST_DATA,
                error_message=f"Department with code '{entity.code}' already exists for property {entity.property_id}"
            )

        try:
            return self.repository.update(entity)
        except IntegrityError as e:
            error_str = str(e.orig)
            if "property_id" in error_str:
                raise ValidationException(
                    error_code=error_codes.INVALID_REQUEST_DATA,
                    error_message=f"Property with id '{entity.property_id}' does not exist"
                )
            elif "parent_id" in error_str:
                raise ValidationException(
                    error_code=error_codes.INVALID_REQUEST_DATA,
                    error_message=f"Parent department with id '{entity.parent_id}' does not exist"
                )
        raise

    def get_department_by_id(self, department_id: int) -> Optional[PropertyDepartmentEntity]:
        """Get department by ID"""
        return self.repository.get_by_id(department_id)

    def get_departments_by_property(self, property_id: str, active_only: bool = True) -> List[PropertyDepartmentEntity]:
        """Get departments for a property"""
        return self.repository.get_by_property(property_id, active_only)

    def get_root_departments(self, property_id: str) -> List[PropertyDepartmentEntity]:
        """Get root departments for a property"""
        return self.repository.get_root_departments(property_id)

    def delete_department(self, department_id: int) -> bool:
        if self.repository.has_child_departments(department_id):
            raise ValidationException(error_codes.INVALID_REQUEST_DATA, "Department has child departments")
        return self.repository.delete(department_id)

    def get_department_id_from_template_code(self, property_id: str, template_code: str) -> Optional[int]:
        """Get department ID from template code"""
        return self.repository.get_department_id_from_template_code(property_id, template_code)

    def get_default_department_id(self, property_id: str) -> Optional[int]:
        """Get default department ID for a property (first active department)"""
        departments = self.get_departments_by_property(property_id, active_only=True)
        if departments:
            return departments[0].id
        return None

