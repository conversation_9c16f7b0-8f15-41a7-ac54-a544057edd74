"""
Property Onboarding Domain Entity
Property onboarding process tracking with business logic.
"""

from datetime import datetime, UTC
from typing import Optional, List, Dict, Any

from pydantic import Field
from pydantic.types import PositiveInt

from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class PropertyOnboardingEntity(BaseDomainEntity):
    """Property onboarding process domain entity"""

    property_id: str = Field(
        ..., min_length=1, max_length=50, description="Property ID"
    )
    brand_id: PositiveInt = Field(..., description="Brand ID (inherited from property)")
    departments_created: int = Field(0, description="Number of departments created")
    profit_centers_created: int = Field(
        0, description="Number of profit centers created"
    )
    skus_created: int = Field(0, description="Number of SKUs created")
    property_skus_created: int = Field(0, description="Number of property SKUs created")
    seller_skus_created: int = Field(0, description="Number of seller SKUs created")
    transaction_codes_generated: int = Field(
        0, description="Number of transaction codes generated"
    )
    onboarding_status: str = Field("PENDING", description="Onboarding status")
    created_entities: Dict[str, List[str]] = Field(
        default_factory=dict, description="Created entity codes"
    )
    errors: List[str] = Field(default_factory=list, description="Onboarding errors")
    warnings: List[str] = Field(default_factory=list, description="Onboarding warnings")
    onboarded_at: Optional[datetime] = None

    def is_completed(self) -> bool:
        """Business rule: Check if onboarding is completed successfully"""
        return self.onboarding_status == "COMPLETED" and len(self.errors) == 0

    def has_warnings(self) -> bool:
        """Business rule: Check if onboarding has warnings"""
        return len(self.warnings) > 0

    def get_total_entities_created(self) -> int:
        """Business rule: Get total number of entities created"""
        return (
            self.departments_created + self.profit_centers_created + self.skus_created +
            self.property_skus_created + self.seller_skus_created
        )

    def add_error(self, error: str) -> None:
        """Add an error to the onboarding process"""
        self.errors.append(error)
        if self.onboarding_status == "COMPLETED":
            self.onboarding_status = "FAILED"

    def add_warning(self, warning: str) -> None:
        """Add a warning to the onboarding process"""
        self.warnings.append(warning)

    def add_created_entity(self, entity_type: str, entity_codes: List[str]) -> None:
        """Add created entity codes to the tracking"""
        if entity_type not in self.created_entities:
            self.created_entities[entity_type] = []
        self.created_entities[entity_type].extend(entity_codes)

    def mark_completed(self) -> None:
        """Mark onboarding as completed"""
        if len(self.errors) == 0:
            self.onboarding_status = "COMPLETED"
            self.onboarded_at = datetime.now(UTC)
        else:
            self.onboarding_status = "FAILED"
